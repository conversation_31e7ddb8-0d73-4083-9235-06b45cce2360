<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>coal-project</artifactId>
        <groupId>com.erdos.coal</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>coal-park</artifactId>

    <version>1.0-SNAPSHOT</version>
    <name>coal-park</name>
    <description>coal-park for erdos.coal</description>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <aliyun.sdk.version>4.0.6</aliyun.sdk.version>
        <aliyun.sdk.api.version>1.1.0</aliyun.sdk.api.version>
        <aliyun.sdk.push.version>3.10.1</aliyun.sdk.push.version>

        <java-jwt.version>3.7.0</java-jwt.version>
    </properties>

    <dependencies>
        <!--健康检查-->
        <!--consul中健康检查需要用到actuator，不添加会check failing-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-thymeleaf</artifactId>-->
        <!--</dependency>-->

        <!--httpclient支持-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>

        <!-- ======================================================================================================= -->
        <!--  Swagger2 API 文档包 -->
        <!--<dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger2.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger2.version}</version>
        </dependency>-->

        <!-- ======================================================================================================= -->
        <!-- 整合消息队列ActiveMQ -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-activemq</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; 如果配置线程池则加入 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.activemq</groupId>-->
        <!--            <artifactId>activemq-pool</artifactId>-->
        <!--        </dependency>-->
        <!-- ======================================================================================================= -->
        <!--图片存储 -->
        <dependency>
            <groupId>com.github.tobato</groupId>
            <artifactId>fastdfs-client</artifactId>
            <version>1.27.2</version>
        </dependency>

        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-ds-mongo</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-aliyun</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-transaction</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-map</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-ocr</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--读文件-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.0.0</version>
        </dependency>

        <!-- 华为短信 -->
        <dependency>
            <groupId>org.openeuler</groupId>
            <artifactId>bgmprovider</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <systemPath>${project.basedir}/lib/java-sdk-core-3.2.4.jar</systemPath>
            <groupId>com.huawei.apigateway</groupId>
            <artifactId>java-sdk-core</artifactId>
            <version>3.2.4</version>
            <scope>system</scope>
        </dependency>
        <!-- 华为语音 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>
        <!-- 图片处理库 -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.20</version>
        </dependency>

        <dependency>
            <groupId>com.erdos.coal</groupId>
            <artifactId>coal-crawler</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>