# 煤炭指数爬虫数据库表设计文档

## 概述

本文档详细说明了煤炭指数爬虫系统的MongoDB数据库表设计，包括实体结构、索引设计和设计理念。

## 数据库表结构

### 1. coal_index_data（煤炭指数基础数据表）

**用途**：存储爬取的原始数据和计算后的衍生数据

**字段说明**：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| _id | ObjectId | MongoDB主键 | - |
| indexType | String | 指数类型：SHENHUA/CCTD/CCI | "SHENHUA" |
| dataDate | Date | 数据日期 | 2025-07-15 |
| calorificValue | Integer | 热值（大卡），非数字类型为null | 4500 |
| coalType | String | 煤种类型标识 | "外购4500" |
| coalCategory | String | 煤种分类 | "外购" |
| price | BigDecimal | 价格（元/吨） | 298.00 |
| changeRate | BigDecimal | 涨跌幅（%） | 2.68 |
| trendDirection | String | 涨跌趋势：UP/DOWN/FLAT | "UP" |
| changeAmount | BigDecimal | 较上期变化金额（元） | 8.00 |
| previousPrice | BigDecimal | 上期价格（元/吨） | 290.00 |
| sourceUrl | String | 数据来源URL | "https://www.meiyibao.com/" |
| crawlTime | Date | 爬取时间 | 2025-07-15 10:30:00 |
| status | Integer | 数据状态：1-有效，0-无效 | 1 |
| memberVisible | Boolean | 是否对会员可见 | true |
| remark | String | 备注信息 | - |
| createTime | Date | 创建时间 | - |
| updateTime | Long | 更新时间戳 | - |

**索引设计**：
- `idx_type_date_coaltype`：(indexType, dataDate, coalType) - 唯一索引，确保数据唯一性
- `idx_data_date`：(dataDate) - 按日期查询
- `idx_type_date`：(indexType, dataDate) - 按类型和日期查询
- `idx_type_category`：(indexType, coalCategory) - 按类型和分类查询
- `idx_type_date_caloric`：(indexType, dataDate, calorificValue) - 兼容旧版本查询

### 2. coal_index_history（煤炭指数历史数据表）

**用途**：专门用于存储折线图展示的历史数据

**字段说明**：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| _id | ObjectId | MongoDB主键 | - |
| indexType | String | 指数类型：SHENHUA/CCTD/CCI | "SHENHUA" |
| calorificValue | Integer | 热值（大卡） | 4500 |
| coalType | String | 煤种类型标识 | "外购4500" |
| coalCategory | String | 煤种分类 | "外购" |
| dataDate | Date | 数据日期 | 2025-07-15 |
| price | BigDecimal | 价格（元/吨） | 298.00 |
| chartX | Integer | 图表X轴位置（可选） | 1 |
| chartY | Integer | 图表Y轴位置（可选） | 298 |
| status | Integer | 数据状态：1-有效，0-无效 | 1 |
| sortOrder | Integer | 排序字段 | 1 |
| createTime | Date | 创建时间 | - |
| updateTime | Long | 更新时间戳 | - |

**索引设计**：
- `idx_history_type_coaltype_date`：(indexType, coalType, dataDate) - 主要查询索引
- `idx_history_type_caloric_date`：(indexType, calorificValue, dataDate) - 兼容查询
- `idx_history_date`：(dataDate) - 按日期查询

### 3. coal_crawl_log（爬取日志表）

**用途**：记录爬取操作的日志信息

**字段说明**：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| _id | ObjectId | MongoDB主键 | - |
| indexType | String | 指数类型：SHENHUA/CCTD/CCI | "SHENHUA" |
| startTime | Date | 爬取开始时间 | 2025-07-15 10:30:00 |
| endTime | Date | 爬取结束时间 | 2025-07-15 10:31:30 |
| crawlStatus | Integer | 爬取状态：0-失败，1-成功 | 1 |
| sourceUrl | String | 目标URL | "https://www.meiyibao.com/" |
| dataCount | Integer | 爬取到的数据条数 | 8 |
| errorMessage | String | 错误信息（如果失败） | - |
| duration | Long | 执行耗时（毫秒） | 90000 |
| crawlerVersion | String | 爬虫版本或标识 | "v1.0.0" |
| logDetail | String | 详细日志信息 | - |
| createTime | Date | 创建时间 | - |
| updateTime | Long | 更新时间戳 | - |

**索引设计**：
- `idx_log_type_time`：(indexType, startTime) - 按类型和时间查询
- `idx_log_status`：(crawlStatus) - 按状态查询

## 数据示例

### 神华外购指数数据示例

```json
{
  "_id": ObjectId("..."),
  "indexType": "SHENHUA",
  "dataDate": ISODate("2025-07-15T00:00:00Z"),
  "calorificValue": 4500,
  "coalType": "外购4500",
  "coalCategory": "外购",
  "price": NumberDecimal("298.00"),
  "changeRate": NumberDecimal("2.68"),
  "trendDirection": "UP",
  "changeAmount": NumberDecimal("8.00"),
  "previousPrice": NumberDecimal("290.00"),
  "sourceUrl": "https://www.meiyibao.com/",
  "crawlTime": ISODate("2025-07-15T10:30:00Z"),
  "status": 1,
  "memberVisible": true,
  "createTime": ISODate("2025-07-15T10:30:00Z"),
  "updateTime": NumberLong(1721034600000)
}
```

### 神华外购神优2数据示例

```json
{
  "_id": ObjectId("..."),
  "indexType": "SHENHUA",
  "dataDate": ISODate("2025-07-15T00:00:00Z"),
  "calorificValue": null,
  "coalType": "外购神优2",
  "coalCategory": "外购",
  "price": NumberDecimal("474.00"),
  "changeRate": NumberDecimal("1.69"),
  "trendDirection": "UP",
  "changeAmount": NumberDecimal("8.00"),
  "previousPrice": NumberDecimal("466.00"),
  "sourceUrl": "https://www.meiyibao.com/",
  "crawlTime": ISODate("2025-07-15T10:30:00Z"),
  "status": 1,
  "memberVisible": true,
  "createTime": ISODate("2025-07-15T10:30:00Z"),
  "updateTime": NumberLong(1721034600000)
}
```

## 设计理念说明

### 1. 为什么这样设计实体

#### 分离关注点
- **CoalIndexData**：存储完整的业务数据，包括原始数据和计算结果
- **CoalIndexHistory**：专门用于图表展示，结构简化，查询性能优化
- **CoalCrawlLog**：独立的日志记录，便于监控和故障排查

#### 数据完整性
- 通过复合唯一索引确保数据不重复
- 支持软删除（status字段）
- 包含数据来源和时间戳信息

#### 性能优化
- 针对不同查询场景设计专门的索引
- 历史数据表专门优化图表查询性能
- 合理的字段类型选择（BigDecimal用于精确计算）

### 2. 新增字段的设计考虑

#### coalType字段
- **目的**：支持非数字热值类型（如"外购神优2"）
- **设计**：字符串类型，可以包含完整的煤种描述
- **示例**："外购4500"、"外购神优2"、"5500kCal"

#### coalCategory字段
- **目的**：对煤种进行分类，便于分组查询和统计
- **设计**：枚举值："外购"、"标准"、"进口"、"其他"
- **用途**：前端可以按分类展示不同的煤种

#### 兼容性考虑
- 保留原有的calorificValue字段，确保向后兼容
- 新增字段可以为null，不影响现有数据
- 提供自动转换逻辑，从coalType中提取热值

### 3. 索引设计原则

#### 查询优化
- 主要查询路径：按指数类型、日期、煤种类型查询
- 唯一性约束：防止重复数据
- 复合索引：支持多条件查询

#### 性能考虑
- 后台索引创建，不影响线上服务
- 合理的索引数量，避免过多索引影响写入性能
- 针对时间序列数据的优化

## 数据流程

### 1. 数据爬取流程
1. 爬虫解析网页数据，提取煤种类型和价格
2. 根据coalType智能识别煤种分类和热值
3. 计算较上期变化和涨跌幅
4. 保存到coal_index_data表
5. 同步更新coal_index_history表（用于图表展示）
6. 记录操作日志到coal_crawl_log表

### 2. 数据查询流程
1. 前端请求特定指数类型的数据
2. 根据查询条件选择合适的索引
3. 返回格式化的数据给前端
4. 前端根据coalCategory分组展示

## 扩展性考虑

### 1. 新增指数类型
- 在IndexType枚举中添加新类型
- 无需修改数据库结构

### 2. 新增煤种类型
- 在CoalType枚举中添加新类型
- 更新常量定义

### 3. 新增字段
- MongoDB的灵活性支持动态添加字段
- 通过实体类注解控制字段映射

## 总结

这个数据库设计充分考虑了煤炭指数数据的特点和业务需求：

1. **灵活性**：支持数字和非数字热值类型
2. **完整性**：包含原始数据和计算结果
3. **性能**：针对查询场景优化索引
4. **可扩展性**：易于添加新的指数类型和煤种
5. **兼容性**：保持向后兼容，平滑升级

通过coalType和coalCategory字段的引入，系统可以完美支持您提到的各种煤种类型，包括"外购4500"、"外购神优2"等，同时保持良好的查询性能和数据完整性。
