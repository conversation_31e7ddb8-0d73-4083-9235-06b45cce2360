package com.erdos.coal.crawler.enums;

/**
 * 煤种类型枚举
 * 用于标识不同的煤种分类和热值
 */
public enum CoalType {
    
    // 神华外购系列
    SHENHUA_WAIGOU_4500("外购4500", "外购", 4500, "神华外购4500大卡"),
    SHENHUA_WAIGOU_5000("外购5000", "外购", 5000, "神华外购5000大卡"),
    SHENHUA_WAIGOU_5500("外购5500", "外购", 5500, "神华外购5500大卡"),
    SHENHUA_WAIGOU_SHENYOU2("外购神优2", "外购", null, "神华外购神优2"),
    
    // 标准热值系列
    STANDARD_4500("4500kCal", "标准", 4500, "4500大卡标准煤"),
    STANDARD_5000("5000kCal", "标准", 5000, "5000大卡标准煤"),
    STANDARD_5500("5500kCal", "标准", 5500, "5500大卡标准煤"),
    STANDARD_5800("5800kCal", "标准", 5800, "5800大卡标准煤"),
    
    // 其他类型（可扩展）
    OTHER("其他", "其他", null, "其他类型煤种");
    
    private final String code;
    private final String category;
    private final Integer calorificValue;
    private final String description;
    
    CoalType(String code, String category, Integer calorificValue, String description) {
        this.code = code;
        this.category = category;
        this.calorificValue = calorificValue;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getCategory() {
        return category;
    }
    
    public Integer getCalorificValue() {
        return calorificValue;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static CoalType fromCode(String code) {
        if (code == null) {
            return OTHER;
        }
        
        for (CoalType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        // 尝试模糊匹配
        for (CoalType type : values()) {
            if (code.contains(type.getCode()) || type.getCode().contains(code)) {
                return type;
            }
        }
        
        return OTHER;
    }
    
    /**
     * 根据热值获取对应的标准煤种类型
     */
    public static CoalType fromCalorificValue(Integer calorificValue) {
        if (calorificValue == null) {
            return OTHER;
        }
        
        for (CoalType type : values()) {
            if (calorificValue.equals(type.getCalorificValue())) {
                return type;
            }
        }
        
        return OTHER;
    }
    
    /**
     * 智能解析煤种类型
     * 支持多种格式：外购4500、4500kCal、外购神优2等
     */
    public static CoalType parseCoalType(String input) {
        if (input == null || input.trim().isEmpty()) {
            return OTHER;
        }
        
        String normalized = input.trim();
        
        // 直接匹配
        CoalType directMatch = fromCode(normalized);
        if (directMatch != OTHER) {
            return directMatch;
        }
        
        // 模糊匹配神华外购系列
        if (normalized.contains("外购")) {
            if (normalized.contains("4500")) {
                return SHENHUA_WAIGOU_4500;
            } else if (normalized.contains("5000")) {
                return SHENHUA_WAIGOU_5000;
            } else if (normalized.contains("5500")) {
                return SHENHUA_WAIGOU_5500;
            } else if (normalized.contains("神优2")) {
                return SHENHUA_WAIGOU_SHENYOU2;
            }
        }
        
        // 模糊匹配标准热值系列
        if (normalized.contains("kCal") || normalized.matches(".*\\d{4}.*")) {
            if (normalized.contains("4500")) {
                return STANDARD_4500;
            } else if (normalized.contains("5000")) {
                return STANDARD_5000;
            } else if (normalized.contains("5500")) {
                return STANDARD_5500;
            } else if (normalized.contains("5800")) {
                return STANDARD_5800;
            }
        }
        
        return OTHER;
    }
    
    /**
     * 检查是否为神华外购系列
     */
    public boolean isShenhuaWaigou() {
        return this.category.equals("外购");
    }
    
    /**
     * 检查是否为标准热值系列
     */
    public boolean isStandard() {
        return this.category.equals("标准");
    }
}
