package com.erdos.coal.crawler.controller;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.ChartDataExtractionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图表数据提取控制器
 * 提供图表数据提取的REST API接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
@RequestMapping("/api/coal/chart")
@CrossOrigin(origins = "*")
public class ChartDataExtractionController {

    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractionController.class);

    @Autowired
    private ChartDataExtractionService chartDataExtractionService;

    /**
     * 提取指定类型的图表数据
     */
    @PostMapping("/extract/{indexType}")
    public ResponseEntity<Map<String, Object>> extractChartData(@PathVariable String indexType) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            IndexType type = IndexType.fromCode(indexType.toUpperCase());
            
            logger.info("开始提取{}图表数据...", type.getName());
            
            List<CoalIndexDataDto> data = chartDataExtractionService.extractChartData(type);
            
            response.put("success", true);
            response.put("indexType", type.getName());
            response.put("dataCount", data.size());
            response.put("data", data);
            response.put("message", "图表数据提取成功");
            
            logger.info("{}图表数据提取完成，共{}条数据", type.getName(), data.size());
            
        } catch (IllegalArgumentException e) {
            logger.error("无效的指数类型: {}", indexType);
            response.put("success", false);
            response.put("message", "无效的指数类型: " + indexType);
        } catch (Exception e) {
            logger.error("提取图表数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "提取图表数据失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 提取原始格式的图表数据
     */
    @PostMapping("/extract-raw/{indexType}")
    public ResponseEntity<Map<String, Object>> extractRawChartData(@PathVariable String indexType) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            IndexType type = IndexType.fromCode(indexType.toUpperCase());
            
            logger.info("开始提取{}原始图表数据...", type.getName());
            
            Map<String, Map<String, String>> rawData = chartDataExtractionService.extractRawChartData(type);
            
            response.put("success", true);
            response.put("indexType", type.getName());
            response.put("dateCount", rawData.size());
            response.put("data", rawData);
            response.put("message", "原始图表数据提取成功");
            
            logger.info("{}原始图表数据提取完成，共{}天的数据", type.getName(), rawData.size());
            
        } catch (IllegalArgumentException e) {
            logger.error("无效的指数类型: {}", indexType);
            response.put("success", false);
            response.put("message", "无效的指数类型: " + indexType);
        } catch (Exception e) {
            logger.error("提取原始图表数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "提取原始图表数据失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 批量提取所有类型的图表数据
     */
    @PostMapping("/extract-all")
    public ResponseEntity<Map<String, Object>> extractAllChartData() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始批量提取所有类型的图表数据...");
            
            Map<IndexType, List<CoalIndexDataDto>> allData = chartDataExtractionService.extractAllChartData();
            
            int totalCount = allData.values().stream().mapToInt(List::size).sum();
            
            response.put("success", true);
            response.put("totalCount", totalCount);
            response.put("data", allData);
            response.put("message", "批量图表数据提取成功");
            
            logger.info("批量图表数据提取完成，总计{}条数据", totalCount);
            
        } catch (Exception e) {
            logger.error("批量提取图表数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量提取图表数据失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 检查图表数据提取功能状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getExtractionStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean available = chartDataExtractionService.isChartExtractionAvailable();
            List<IndexType> supportedTypes = chartDataExtractionService.getSupportedIndexTypes();
            
            response.put("available", available);
            response.put("supportedTypes", supportedTypes);
            response.put("message", available ? "图表数据提取功能正常" : "图表数据提取功能不可用");
            
        } catch (Exception e) {
            logger.error("获取提取状态失败: {}", e.getMessage(), e);
            response.put("available", false);
            response.put("message", "获取提取状态失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取支持的指数类型列表
     */
    @GetMapping("/supported-types")
    public ResponseEntity<Map<String, Object>> getSupportedTypes() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<IndexType> supportedTypes = chartDataExtractionService.getSupportedIndexTypes();
            
            response.put("success", true);
            response.put("supportedTypes", supportedTypes);
            response.put("count", supportedTypes.size());
            
        } catch (Exception e) {
            logger.error("获取支持的指数类型失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取支持的指数类型失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试图表数据提取功能
     */
    @PostMapping("/test/{indexType}")
    public ResponseEntity<Map<String, Object>> testChartExtraction(@PathVariable String indexType) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            IndexType type = IndexType.fromCode(indexType.toUpperCase());
            
            logger.info("开始测试{}图表数据提取功能...", type.getName());
            
            long startTime = System.currentTimeMillis();
            List<CoalIndexDataDto> data = chartDataExtractionService.extractChartData(type);
            long endTime = System.currentTimeMillis();
            
            response.put("success", true);
            response.put("indexType", type.getName());
            response.put("dataCount", data.size());
            response.put("extractionTime", endTime - startTime);
            response.put("message", "图表数据提取测试成功");
            
            // 只返回前5条数据作为示例
            if (data.size() > 5) {
                response.put("sampleData", data.subList(0, 5));
            } else {
                response.put("sampleData", data);
            }
            
            logger.info("{}图表数据提取测试完成，耗时{}ms，共{}条数据", 
                       type.getName(), endTime - startTime, data.size());
            
        } catch (IllegalArgumentException e) {
            logger.error("无效的指数类型: {}", indexType);
            response.put("success", false);
            response.put("message", "无效的指数类型: " + indexType);
        } catch (Exception e) {
            logger.error("测试图表数据提取失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "测试图表数据提取失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
}
