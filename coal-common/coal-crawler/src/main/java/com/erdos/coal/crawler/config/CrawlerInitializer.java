// package com.erdos.coal.crawler.config;
//
// import com.erdos.coal.core.utils.EntityTools;
// import dev.morphia.Datastore;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.boot.ApplicationArguments;
// import org.springframework.boot.ApplicationRunner;
// import org.springframework.data.mongodb.core.MongoTemplate;
// import org.springframework.stereotype.Component;
//
// import javax.annotation.Resource;
// import java.util.List;
// import java.util.Set;
//
// /**
//  * 爬虫模块初始化器
//  * 负责创建MongoDB集合和索引
//  */
// @Component
// public class CrawlerInitializer implements ApplicationRunner {
//
//     private static final Logger logger = LoggerFactory.getLogger(CrawlerInitializer.class);
//
//     @Resource(name = "dsForCoal")
//     private Datastore datastore;
//
//     @Resource
//     private MongoTemplate mongoTemplate;
//
//     /**
//      * 需要扫描的包路径
//      */
//     private final static String[] basePackages = {
//         "com.erdos.coal.crawler"
//     };
//
//     @Override
//     public void run(ApplicationArguments args) throws Exception {
//         logger.info("---------- 爬虫模块初始化开始... ----------");
//
//         try {
//             initCrawlerCollections();
//             logger.info("========== 爬虫模块初始化完成. ==========");
//         } catch (Exception e) {
//             logger.warn("爬虫模块初始化失败，可能是MongoDB服务未启动: {}", e.getMessage());
//             logger.info("应用程序将继续启动，但MongoDB集合可能需要手动创建或在MongoDB服务启动后重启应用");
//             // 不抛出异常，允许应用程序继续启动
//         }
//     }
//
//     /**
//      * 初始化爬虫相关的MongoDB集合
//      */
//     private void initCrawlerCollections() {
//         try {
//             // 扫描实体类
//             Set<Class<?>> classSet = new java.util.HashSet<>();
//             for (String packageName : basePackages) {
//                 Set<Class<?>> packageClasses = EntityTools.getEntityCollectionClass(packageName);
//                 classSet.addAll(packageClasses);
//                 logger.info("扫描包: {} 找到 {} 个实体类", packageName, packageClasses.size());
//             }
//
//             // 添加实体类映射
//             classSet.forEach((c) -> {
//                 datastore.getMapper().addMappedClass(c);
//                 logger.info("添加实体类映射: {}", c.getSimpleName());
//             });
//
//             // 创建固定集合（如果有的话）
//             datastore.ensureCaps();
//
//             // 创建集合
//             List<String> collections = EntityTools.getCollectionNamesByEntityCollectionClass(classSet);
//             logger.info("准备创建 {} 个集合: {}", collections.size(), collections);
//
//             for (String coll : collections) {
//                 try {
//                     // 判断集合是否存在
//                     if (!mongoTemplate.collectionExists(coll)) {
//                         // 创建集合
//                         mongoTemplate.createCollection(coll);
//                         logger.info("集合: {} 不存在，已创建!", coll);
//                     } else {
//                         logger.info("集合: {} 已存在", coll);
//                     }
//                 } catch (Exception e) {
//                     logger.warn("创建集合 {} 失败: {}", coll, e.getMessage());
//                     throw e; // 重新抛出异常以便上层处理
//                 }
//             }
//
//             // 生成索引
//             try {
//                 datastore.ensureIndexes();
//                 logger.info("索引创建完成");
//             } catch (Exception e) {
//                 logger.warn("创建索引失败: {}", e.getMessage());
//                 throw e; // 重新抛出异常以便上层处理
//             }
//
//         } catch (Exception e) {
//             logger.error("初始化爬虫集合失败", e);
//             throw e; // 让上层的run方法处理
//         }
//     }
// }
