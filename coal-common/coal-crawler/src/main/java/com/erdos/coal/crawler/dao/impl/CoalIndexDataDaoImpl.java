package com.erdos.coal.crawler.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.crawler.dao.CoalIndexDataDao;
import com.erdos.coal.crawler.entity.CoalIndexData;
import com.erdos.coal.crawler.enums.IndexType;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 煤炭指数数据DAO实现类
 */
@Repository
public class CoalIndexDataDaoImpl extends BaseMongoDAOImpl<CoalIndexData> implements CoalIndexDataDao {
    
    @Override
    public CoalIndexData save(CoalIndexData data) {
        return super.save(data);
    }
    
    @Override
    public void saveBatch(List<CoalIndexData> dataList) {
        if (dataList != null && !dataList.isEmpty()) {
            for (CoalIndexData data : dataList) {
                save(data);
            }
        }
    }
    
    @Override
    public CoalIndexData findByTypeAndDateAndCaloric(IndexType indexType, Date dataDate, Integer calorificValue) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("dataDate").equal(dataDate)
                .field("calorificValue").equal(calorificValue)
                .field("status").equal(1);
        return query.first();
    }

    @Override
    public CoalIndexData findByTypeAndDateAndCoalType(IndexType indexType, Date dataDate, String coalType) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("dataDate").equal(dataDate)
                .field("coalType").equal(coalType)
                .field("status").equal(1);
        return query.first();
    }
    
    @Override
    public List<CoalIndexData> findByTypeAndDateRange(IndexType indexType, Date startDate, Date endDate) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("status").equal(1);
        
        if (startDate != null) {
            query.field("dataDate").greaterThanOrEq(startDate);
        }
        if (endDate != null) {
            query.field("dataDate").lessThanOrEq(endDate);
        }
        
        return query.order(Sort.descending("dataDate")).asList();
    }
    
    @Override
    public List<CoalIndexData> findLatestByType(IndexType indexType) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("status").equal(1)
                .order(Sort.descending("dataDate"))
                .limit(10);
        return query.asList();
    }
    
    @Override
    public CoalIndexData findLatestByTypeAndCaloric(IndexType indexType, Integer calorificValue) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("calorificValue").equal(calorificValue)
                .field("status").equal(1)
                .order(Sort.descending("dataDate"))
                .limit(1);
        return query.first();
    }

    @Override
    public CoalIndexData findLatestByTypeAndCoalType(IndexType indexType, String coalType) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("coalType").equal(coalType)
                .field("status").equal(1)
                .order(Sort.descending("dataDate"))
                .limit(1);
        return query.first();
    }

    @Override
    public List<CoalIndexData> findByTypeAndCategory(IndexType indexType, String coalCategory) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("coalCategory").equal(coalCategory)
                .field("status").equal(1)
                .order(Sort.descending("dataDate"));
        return query.asList();
    }
    
    @Override
    public List<CoalIndexData> findHistoryByType(IndexType indexType, int limit) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("status").equal(1)
                .order(Sort.descending("dataDate"))
                .limit(limit);
        return query.asList();
    }
    
    @Override
    public void update(CoalIndexData data) {
        super.save(data);
    }
    
    @Override
    public CoalIndexData delete(String id) {
        return super.delete(id);
    }

    @Override
    public CoalIndexData findById(String id) {
        return super.getByPK(id);
    }
    
    @Override
    public long countByTypeAndDateRange(IndexType indexType, Date startDate, Date endDate) {
        Query<CoalIndexData> query = createQuery()
                .field("indexType").equal(indexType)
                .field("status").equal(1);
        
        if (startDate != null) {
            query.field("dataDate").greaterThanOrEq(startDate);
        }
        if (endDate != null) {
            query.field("dataDate").lessThanOrEq(endDate);
        }
        
        return query.count();
    }
}
