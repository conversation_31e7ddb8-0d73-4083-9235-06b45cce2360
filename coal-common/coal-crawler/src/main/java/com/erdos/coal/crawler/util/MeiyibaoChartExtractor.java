package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 煤易宝图表数据提取器
 * 专门用于提取煤易宝网站图表数据的工具类
 * 支持神华外购、CCI指数、CCTD指数三种数据类型的提取
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public class MeiyibaoChartExtractor {

    private static final Logger logger = LoggerFactory.getLogger(MeiyibaoChartExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");

    /**
     * 数据去重辅助类
     */
    private static class DataDeduplicator {
        private final Set<String> processedDataItems = new HashSet<>();
        private final Map<String, Map<String, String>> uniqueData = new LinkedHashMap<>();
        private int processedCount = 0;

        public boolean addData(String date, String calorific, String price, String rawData) {
            processedCount++;
            logger.debug("尝试添加数据: {} - {}={}", date, calorific, price);

            // 创建数据项的唯一标识符（基于日期+热值+价格）
            String dataItemKey = date + "|" + calorific + "|" + price;

            // 检查具体数据项是否已处理
            if (processedDataItems.contains(dataItemKey)) {
                logger.debug("相同数据项已处理，跳过: {} - {}={}", date, calorific, price);
                return false;
            }

            // 检查是否为有效数据
            if (!isValidData(calorific, price)) {
                logger.debug("数据验证失败: {} = {}", calorific, price);
                return false;
            }

            Map<String, String> dateData = uniqueData.computeIfAbsent(date, k -> new LinkedHashMap<>());

            // 检查是否已有相同的数据（双重保险）
            if (dateData.containsKey(calorific) && dateData.get(calorific).equals(price)) {
                logger.debug("相同数据已存在，跳过: {} - {}={}", date, calorific, price);
                return false;
            }

            // 添加新数据
            dateData.put(calorific, price);
            processedDataItems.add(dataItemKey);
            logger.debug("添加新数据: {} - {}={}", date, calorific, price);
            return true;
        }

        private boolean isValidData(String calorific, String price) {
            try {
                // 提取价格数字
                String priceNum = price.replaceAll("[^0-9]", "");
                if (priceNum.isEmpty()) return false;

                int priceValue = Integer.parseInt(priceNum);

                // 扩展价格范围验证（150-1200元，覆盖更多可能的价格）
                if (priceValue < 150 || priceValue > 1200) {
                    logger.debug("价格超出有效范围: {}", priceValue);
                    return false;
                }

                // 扩展热值验证，支持更多格式
                boolean validCalorific = calorific.contains("5500kCal") || calorific.contains("5000kCal") ||
                                       calorific.contains("4500kCal") || calorific.contains("5800kCal") ||
                                       calorific.contains("外购") || calorific.contains("神优") ||
                                       calorific.contains("5500") || calorific.contains("5000") ||
                                       calorific.contains("4500") || calorific.contains("5800");

                if (!validCalorific) {
                    logger.debug("热值格式无效: {}", calorific);
                    return false;
                }

                // 排除明显的无关数据
                String lowerCalorific = calorific.toLowerCase();
                String lowerPrice = price.toLowerCase();
                if (lowerCalorific.contains("app") || lowerCalorific.contains("下载") ||
                    lowerPrice.contains("app") || lowerPrice.contains("下载")) {
                    logger.debug("排除无关数据: {} = {}", calorific, price);
                    return false;
                }

                return true;

            } catch (Exception e) {
                logger.debug("数据验证异常: {}", e.getMessage());
                return false;
            }
        }

        public Map<String, Map<String, String>> getUniqueData() {
            return uniqueData;
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }

    /**
     * 提取煤易宝数据的主方法
     *
     * @param indexType 指数类型（SHENHUA/CCI/CCTD）
     * @return 提取到的数据，格式：Map<日期, Map<热值, 价格>>
     */
    public static Map<String, Map<String, String>> extractMeiyibaoData(IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000));

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"));

            Page page = context.newPage();
            page.setDefaultTimeout(120000);

            try {
                logger.info("开始提取{}数据...", indexType.getName());

                // 访问煤易宝网站
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));
                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 点击对应的标签页
                clickIndexTab(page, indexType);
                page.waitForTimeout(3000);

                // 从Canvas折线图提取数据
                result = extractDataFromCanvas(page);

                logger.info("{}数据提取完成，共获取{}天的数据", indexType.getName(), result.size());

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
            }

            page.close();
            context.close();
            browser.close();

        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 兼容测试类的方法，返回CoalIndexDataDto列表格式
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> result = new ArrayList<>();

        Map<String, Map<String, String>> rawData = extractMeiyibaoData(indexType);

        // 转换数据格式
        for (Map.Entry<String, Map<String, String>> dateEntry : rawData.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String calorific = priceEntry.getKey();
                String price = priceEntry.getValue();

                try {
                    CoalIndexDataDto dto = new CoalIndexDataDto();

                    // 解析日期字符串为Date对象
                    Date dataDate = parseDate(date);
                    dto.setDataDate(dataDate);

                    // 解析热值
                    Integer calorificValue = parseCalorificValue(calorific);
                    dto.setCalorificValue(calorificValue);

                    // 解析价格
                    BigDecimal priceValue = new BigDecimal(price.replaceAll("[^0-9]", ""));
                    dto.setPrice(priceValue);

                    dto.setIndexType(indexType);
                    dto.setSourceUrl(BASE_URL);
                    dto.setCoalType(calorific);
                    result.add(dto);
                } catch (Exception e) {
                    logger.debug("转换数据格式失败: {}", e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 解析日期字符串为Date对象
     */
    private static Date parseDate(String dateStr) {
        try {
            // 假设是当前年份的日期
            Calendar cal = Calendar.getInstance();
            int currentYear = cal.get(Calendar.YEAR);
            
            // 解析MM-dd格式
            Date date = DATE_FORMAT.parse(dateStr);
            cal.setTime(date);
            cal.set(Calendar.YEAR, currentYear);
            
            return cal.getTime();
        } catch (Exception e) {
            logger.debug("日期解析失败: {}", dateStr);
            return new Date(); // 返回当前日期作为默认值
        }
    }

    /**
     * 从热值字符串中解析数值
     */
    private static Integer parseCalorificValue(String calorific) {
        try {
            // 提取数字
            Pattern pattern = Pattern.compile("(\\d{4,5})");
            Matcher matcher = pattern.matcher(calorific);
            if (matcher.find()) {
                return Integer.parseInt(matcher.group(1));
            }
        } catch (Exception e) {
            logger.debug("热值解析失败: {}", calorific);
        }
        return null;
    }

    /**
     * 点击指定的指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabText = getTabText(indexType);
            logger.info("尝试点击{}标签页...", tabText);

            // 尝试多种选择器策略
            String[] selectors = {
                "text=" + tabText,
                "[data-tab='" + indexType.getCode().toLowerCase() + "']",
                "#tab-" + getTabId(indexType),
                ".tab-item:has-text('" + tabText + "')",
                "a:has-text('" + tabText + "')",
                "button:has-text('" + tabText + "')"
            };

            boolean clicked = false;
            for (String selector : selectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        page.click(selector);
                        page.waitForTimeout(3000);
                        logger.info("成功点击{}标签页", tabText);
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}失败: {}", selector, e.getMessage());
                }
            }

            if (!clicked) {
                logger.warn("所有选择器都失败，使用默认标签页");
            }

        } catch (Exception e) {
            logger.warn("点击{}标签页失败: {}", indexType.getName(), e.getMessage());
        }
    }

    /**
     * 获取标签页文本
     */
    private static String getTabText(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "神华外购指数";
            case CCI:
                return "CCI指数";
            case CCTD:
                return "CCTD指数";
            default:
                return "神华外购指数";
        }
    }

    /**
     * 获取标签页ID
     */
    private static String getTabId(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "1";
            case CCI:
                return "2";
            case CCTD:
                return "3";
            default:
                return "3";
        }
    }
