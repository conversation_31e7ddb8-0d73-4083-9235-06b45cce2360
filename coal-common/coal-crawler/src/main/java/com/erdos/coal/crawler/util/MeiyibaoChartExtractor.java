package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 煤易宝图表数据提取器
 * 专门用于提取煤易宝网站图表数据的工具类
 * 支持神华外购、CCI指数、CCTD指数三种数据类型的提取
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public class MeiyibaoChartExtractor {

    private static final Logger logger = LoggerFactory.getLogger(MeiyibaoChartExtractor.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");

    /**
     * 数据去重辅助类
     */
    private static class DataDeduplicator {
        private final Set<String> processedDataItems = new HashSet<>();
        private final Map<String, Map<String, String>> uniqueData = new LinkedHashMap<>();
        private int processedCount = 0;

        public boolean addData(String date, String calorific, String price, String rawData) {
            processedCount++;
            logger.debug("尝试添加数据: {} - {}={}", date, calorific, price);

            // 创建数据项的唯一标识符（基于日期+热值+价格）
            String dataItemKey = date + "|" + calorific + "|" + price;

            // 检查具体数据项是否已处理
            if (processedDataItems.contains(dataItemKey)) {
                logger.debug("相同数据项已处理，跳过: {} - {}={}", date, calorific, price);
                return false;
            }

            // 检查是否为有效数据
            if (!isValidData(calorific, price)) {
                logger.debug("数据验证失败: {} = {}", calorific, price);
                return false;
            }

            Map<String, String> dateData = uniqueData.computeIfAbsent(date, k -> new LinkedHashMap<>());

            // 检查是否已有相同的数据（双重保险）
            if (dateData.containsKey(calorific) && dateData.get(calorific).equals(price)) {
                logger.debug("相同数据已存在，跳过: {} - {}={}", date, calorific, price);
                return false;
            }

            // 添加新数据
            dateData.put(calorific, price);
            processedDataItems.add(dataItemKey);
            logger.debug("添加新数据: {} - {}={}", date, calorific, price);
            return true;
        }

        private boolean isValidData(String calorific, String price) {
            try {
                // 提取价格数字
                String priceNum = price.replaceAll("[^0-9]", "");
                if (priceNum.isEmpty()) return false;

                int priceValue = Integer.parseInt(priceNum);

                // 扩展价格范围验证（150-1200元，覆盖更多可能的价格）
                if (priceValue < 150 || priceValue > 1200) {
                    logger.debug("价格超出有效范围: {}", priceValue);
                    return false;
                }

                // 扩展热值验证，支持更多格式
                boolean validCalorific = calorific.contains("5500kCal") || calorific.contains("5000kCal") ||
                                       calorific.contains("4500kCal") || calorific.contains("5800kCal") ||
                                       calorific.contains("外购") || calorific.contains("神优") ||
                                       calorific.contains("5500") || calorific.contains("5000") ||
                                       calorific.contains("4500") || calorific.contains("5800");

                if (!validCalorific) {
                    logger.debug("热值格式无效: {}", calorific);
                    return false;
                }

                // 排除明显的无关数据
                String lowerCalorific = calorific.toLowerCase();
                String lowerPrice = price.toLowerCase();
                if (lowerCalorific.contains("app") || lowerCalorific.contains("下载") ||
                    lowerPrice.contains("app") || lowerPrice.contains("下载")) {
                    logger.debug("排除无关数据: {} = {}", calorific, price);
                    return false;
                }

                return true;

            } catch (Exception e) {
                logger.debug("数据验证异常: {}", e.getMessage());
                return false;
            }
        }

        public Map<String, Map<String, String>> getUniqueData() {
            return uniqueData;
        }

        public int getProcessedCount() {
            return processedCount;
        }
    }

    /**
     * 提取煤易宝数据的主方法
     *
     * @param indexType 指数类型（SHENHUA/CCI/CCTD）
     * @return 提取到的数据，格式：Map<日期, Map<热值, 价格>>
     */
    public static Map<String, Map<String, String>> extractMeiyibaoData(IndexType indexType) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 可视化模式便于调试
                            .setTimeout(120000));

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1920, 1080)
                    .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"));

            Page page = context.newPage();
            page.setDefaultTimeout(120000);

            try {
                logger.info("开始提取{}数据...", indexType.getName());

                // 访问煤易宝网站
                page.navigate(BASE_URL, new Page.NavigateOptions().setTimeout(120000));
                logger.info("页面导航成功，等待内容加载...");
                page.waitForTimeout(5000);

                // 检查页面是否加载成功
                String title = page.title();
                logger.info("页面标题: {}", title);

                // 点击对应的标签页
                clickIndexTab(page, indexType);
                page.waitForTimeout(3000);

                // 从Canvas折线图提取数据
                result = extractDataFromCanvas(page);

                logger.info("{}数据提取完成，共获取{}天的数据", indexType.getName(), result.size());

            } catch (Exception e) {
                logger.error("页面操作失败: {}", e.getMessage(), e);
            }

            page.close();
            context.close();
            browser.close();

        } catch (Exception e) {
            logger.error("浏览器操作失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 兼容测试类的方法，返回CoalIndexDataDto列表格式
     */
    public static List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        List<CoalIndexDataDto> result = new ArrayList<>();

        Map<String, Map<String, String>> rawData = extractMeiyibaoData(indexType);

        // 转换数据格式
        for (Map.Entry<String, Map<String, String>> dateEntry : rawData.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, String> dayData = dateEntry.getValue();

            for (Map.Entry<String, String> priceEntry : dayData.entrySet()) {
                String calorific = priceEntry.getKey();
                String price = priceEntry.getValue();

                try {
                    CoalIndexDataDto dto = new CoalIndexDataDto();

                    // 解析日期字符串为Date对象
                    Date dataDate = parseDate(date);
                    dto.setDataDate(dataDate);

                    // 解析热值
                    Integer calorificValue = parseCalorificValue(calorific);
                    dto.setCalorificValue(calorificValue);

                    // 解析价格
                    BigDecimal priceValue = new BigDecimal(price.replaceAll("[^0-9]", ""));
                    dto.setPrice(priceValue);

                    dto.setIndexType(indexType);
                    dto.setSourceUrl(BASE_URL);
                    dto.setCoalType(calorific);
                    result.add(dto);
                } catch (Exception e) {
                    logger.debug("转换数据格式失败: {}", e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 解析日期字符串为Date对象
     */
    private static Date parseDate(String dateStr) {
        try {
            // 假设是当前年份的日期
            Calendar cal = Calendar.getInstance();
            int currentYear = cal.get(Calendar.YEAR);
            
            // 解析MM-dd格式
            Date date = DATE_FORMAT.parse(dateStr);
            cal.setTime(date);
            cal.set(Calendar.YEAR, currentYear);
            
            return cal.getTime();
        } catch (Exception e) {
            logger.debug("日期解析失败: {}", dateStr);
            return new Date(); // 返回当前日期作为默认值
        }
    }

    /**
     * 从热值字符串中解析数值
     */
    private static Integer parseCalorificValue(String calorific) {
        try {
            // 提取数字
            Pattern pattern = Pattern.compile("(\\d{4,5})");
            Matcher matcher = pattern.matcher(calorific);
            if (matcher.find()) {
                return Integer.parseInt(matcher.group(1));
            }
        } catch (Exception e) {
            logger.debug("热值解析失败: {}", calorific);
        }
        return null;
    }

    /**
     * 点击指定的指数标签页
     */
    private static void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabText = getTabText(indexType);
            logger.info("尝试点击{}标签页...", tabText);

            // 尝试多种选择器策略
            String[] selectors = {
                "text=" + tabText,
                "[data-tab='" + indexType.getCode().toLowerCase() + "']",
                "#tab-" + getTabId(indexType),
                ".tab-item:has-text('" + tabText + "')",
                "a:has-text('" + tabText + "')",
                "button:has-text('" + tabText + "')"
            };

            boolean clicked = false;
            for (String selector : selectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        page.click(selector);
                        page.waitForTimeout(3000);
                        logger.info("成功点击{}标签页", tabText);
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    logger.debug("选择器{}失败: {}", selector, e.getMessage());
                }
            }

            if (!clicked) {
                logger.warn("所有选择器都失败，使用默认标签页");
            }

        } catch (Exception e) {
            logger.warn("点击{}标签页失败: {}", indexType.getName(), e.getMessage());
        }
    }

    /**
     * 获取标签页文本
     */
    private static String getTabText(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "神华外购指数";
            case CCI:
                return "CCI指数";
            case CCTD:
                return "CCTD指数";
            default:
                return "神华外购指数";
        }
    }

    /**
     * 获取标签页ID
     */
    private static String getTabId(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "1";
            case CCI:
                return "2";
            case CCTD:
                return "3";
            default:
                return "3";
        }
    }

    /**
     * 从Canvas折线图提取数据
     */
    private static Map<String, Map<String, String>> extractDataFromCanvas(Page page) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();

        try {
            logger.info("开始从Canvas折线图提取数据...");

            // 首先尝试直接从ECharts实例获取数据
            result = extractDataFromEChartsInstance(page);
            if (!result.isEmpty()) {
                logger.info("成功从ECharts实例提取到数据");
                return result;
            }

            // 如果ECharts实例提取失败，尝试通过鼠标交互提取
            logger.info("ECharts实例提取失败，尝试鼠标交互方式...");

            // 首先尝试查找具有特定属性的canvas元素
            Locator targetCanvas = findTargetCanvas(page);
            if (targetCanvas == null) {
                logger.warn("未找到目标canvas元素");
                return result;
            }

            // 获取canvas的边界框
            BoundingBox canvasBounds = targetCanvas.boundingBox();
            if (canvasBounds == null) {
                logger.warn("无法获取canvas的边界框");
                return result;
            }

            logger.info("目标Canvas边界框: x={}, y={}, width={}, height={}",
                       canvasBounds.x, canvasBounds.y, canvasBounds.width, canvasBounds.height);

            // 等待图表加载完成
            page.waitForTimeout(3000);

            // 在canvas上模拟鼠标移动，尝试触发tooltip
            result = extractDataByMouseInteraction(page, targetCanvas, canvasBounds);

        } catch (Exception e) {
            logger.error("从Canvas提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 查找目标Canvas元素
     */
    private static Locator findTargetCanvas(Page page) {
        try {
            // 策略1: 查找具有data-zr-dom-id="zr_0"属性的canvas
            Locator canvasWithZrId = page.locator("canvas[data-zr-dom-id='zr_0']");
            if (canvasWithZrId.count() > 0) {
                logger.info("找到data-zr-dom-id='zr_0'的canvas元素");
                return canvasWithZrId.first();
            }

            // 策略2: 查找coal-char类下的canvas
            Locator coalCharElement = page.locator(".coal-char");
            if (coalCharElement.count() > 0) {
                Locator canvasInCoalChar = coalCharElement.locator("canvas");
                if (canvasInCoalChar.count() > 0) {
                    logger.info("找到coal-char元素中的canvas，数量: {}", canvasInCoalChar.count());
                    return canvasInCoalChar.first();
                }
            }

            // 策略3: 查找所有canvas，选择尺寸最大的（通常是主图表）
            Locator allCanvases = page.locator("canvas");
            if (allCanvases.count() > 0) {
                logger.info("找到所有canvas元素，数量: {}", allCanvases.count());

                // 选择尺寸最大的canvas
                Locator largestCanvas = null;
                double maxArea = 0;

                for (int i = 0; i < allCanvases.count(); i++) {
                    Locator canvas = allCanvases.nth(i);
                    BoundingBox bounds = canvas.boundingBox();
                    if (bounds != null) {
                        double area = bounds.width * bounds.height;
                        logger.debug("Canvas[{}] 尺寸: {}x{}, 面积: {}", i, bounds.width, bounds.height, area);
                        if (area > maxArea) {
                            maxArea = area;
                            largestCanvas = canvas;
                        }
                    }
                }

                if (largestCanvas != null) {
                    logger.info("选择最大的canvas作为目标，面积: {}", maxArea);
                    return largestCanvas;
                }
            }

            logger.warn("未找到任何可用的canvas元素");
            return null;

        } catch (Exception e) {
            logger.error("查找目标canvas失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从ECharts实例直接提取数据
     */
    private static Map<String, Map<String, String>> extractDataFromEChartsInstance(Page page) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("尝试从ECharts实例直接提取数据...");

            // 使用简化的JavaScript获取ECharts数据
            String jsCode = "() => { " +
                "try { " +
                "if (typeof window.echarts === 'undefined') return null; " +
                "var instances = []; " +
                "var canvases = document.querySelectorAll('canvas'); " +
                "for (var i = 0; i < canvases.length; i++) { " +
                "var instance = window.echarts.getInstanceByDom(canvases[i]); " +
                "if (instance) instances.push(instance); " +
                "} " +
                "if (instances.length === 0 && window.echarts._instances) { " +
                "var keys = Object.keys(window.echarts._instances); " +
                "for (var j = 0; j < keys.length; j++) { " +
                "instances.push(window.echarts._instances[keys[j]]); " +
                "} " +
                "} " +
                "var result = []; " +
                "for (var k = 0; k < instances.length; k++) { " +
                "var chart = instances[k]; " +
                "if (chart && chart.getOption) { " +
                "var option = chart.getOption(); " +
                "if (option && option.series && option.series.length > 0) { " +
                "result.push({ " +
                "series: option.series, " +
                "xAxis: option.xAxis, " +
                "yAxis: option.yAxis " +
                "}); " +
                "} " +
                "} " +
                "} " +
                "return result; " +
                "} catch (e) { " +
                "return null; " +
                "} " +
                "}";

            Object chartDataObj = page.evaluate(jsCode);

            if (chartDataObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> charts = (List<Map<String, Object>>) chartDataObj;

                if (!charts.isEmpty()) {
                    logger.info("找到{}个ECharts实例", charts.size());

                    for (Map<String, Object> chart : charts) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> series = (List<Map<String, Object>>) chart.get("series");

                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> xAxis = (List<Map<String, Object>>) chart.get("xAxis");

                        if (series != null && xAxis != null) {
                            extractDataFromSeries(series, xAxis, deduplicator);
                        }
                    }
                }
            }

            result = deduplicator.getUniqueData();
            logger.info("从ECharts实例提取到{}天的数据", result.size());

        } catch (Exception e) {
            logger.error("从ECharts实例提取数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从ECharts系列数据中提取煤炭价格数据
     */
    private static void extractDataFromSeries(List<Map<String, Object>> series,
                                            List<Map<String, Object>> xAxis,
                                            DataDeduplicator deduplicator) {
        try {
            // 获取X轴数据（日期）
            List<String> dates = new ArrayList<>();
            if (!xAxis.isEmpty()) {
                @SuppressWarnings("unchecked")
                List<String> xAxisData = (List<String>) xAxis.get(0).get("data");
                if (xAxisData != null) {
                    dates = xAxisData;
                }
            }

            logger.info("X轴日期数据: {}", dates);

            // 遍历每个系列（每条折线）
            for (Map<String, Object> seriesItem : series) {
                String seriesName = (String) seriesItem.get("name");
                @SuppressWarnings("unchecked")
                List<Object> data = (List<Object>) seriesItem.get("data");

                if (seriesName != null && data != null) {
                    logger.info("处理系列: {}, 数据点数量: {}", seriesName, data.size());

                    // 解析系列名称以确定热值类型
                    String calorificType = parseCalorificFromSeriesName(seriesName);

                    // 遍历数据点
                    for (int i = 0; i < Math.min(data.size(), dates.size()); i++) {
                        Object dataPoint = data.get(i);
                        String date = normalizeDateFormat(dates.get(i));

                        // 提取价格值
                        String price = extractPriceFromDataPoint(dataPoint);

                        if (price != null && !price.isEmpty()) {
                            String rawData = String.format("ECharts_%s_%s_%s", seriesName, date, price);
                            deduplicator.addData(date, calorificType, price + "元", rawData);
                            logger.debug("从ECharts提取数据: {} {} = {}元", date, calorificType, price);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error("从系列数据提取失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从系列名称解析热值类型
     */
    private static String parseCalorificFromSeriesName(String seriesName) {
        if (seriesName == null) return "未知类型";

        String lowerName = seriesName.toLowerCase();
        if (lowerName.contains("5500") || lowerName.contains("外购5500")) {
            return "外购5500kCal";
        } else if (lowerName.contains("5000") || lowerName.contains("外购5000")) {
            return "外购5000kCal";
        } else if (lowerName.contains("4500") || lowerName.contains("外购4500")) {
            return "外购4500kCal";
        } else if (lowerName.contains("神优") || lowerName.contains("外购神优")) {
            return "外购神优2";
        } else if (lowerName.contains("5500kcal")) {
            return "5500kCal";
        } else if (lowerName.contains("5000kcal")) {
            return "5000kCal";
        } else if (lowerName.contains("4500kcal")) {
            return "4500kCal";
        }

        return seriesName; // 如果无法识别，返回原始名称
    }

    /**
     * 从数据点提取价格值
     */
    private static String extractPriceFromDataPoint(Object dataPoint) {
        try {
            if (dataPoint instanceof Number) {
                return String.valueOf(((Number) dataPoint).intValue());
            } else if (dataPoint instanceof String) {
                String str = (String) dataPoint;
                // 提取数字
                Pattern numberPattern = Pattern.compile("\\d+");
                Matcher matcher = numberPattern.matcher(str);
                if (matcher.find()) {
                    return matcher.group();
                }
            } else if (dataPoint instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) dataPoint;
                if (!list.isEmpty() && list.get(list.size() - 1) instanceof Number) {
                    // 通常最后一个元素是Y轴值（价格）
                    return String.valueOf(((Number) list.get(list.size() - 1)).intValue());
                }
            }
        } catch (Exception e) {
            logger.debug("提取价格值失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 标准化日期格式为MM-dd
     */
    private static String normalizeDateFormat(String dateStr) {
        try {
            if (dateStr.matches("\\d{2}-\\d{2}")) {
                return dateStr; // 已经是目标格式
            } else if (dateStr.matches("\\d{1,2}月\\d{1,2}日")) {
                // 7月15日 -> 07-15
                String[] parts = dateStr.replace("月", "-").replace("日", "").split("-");
                return String.format("%02d-%02d", Integer.parseInt(parts[0]), Integer.parseInt(parts[1]));
            } else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                // 2025-07-15 -> 07-15
                return dateStr.substring(5);
            } else if (dateStr.matches("\\d{2}/\\d{2}")) {
                // 07/15 -> 07-15
                return dateStr.replace("/", "-");
            }
        } catch (Exception e) {
            logger.debug("日期格式转换失败: {}", dateStr);
        }
        return dateStr;
    }

    /**
     * 通过鼠标交互提取数据（使用去重辅助类）
     */
    private static Map<String, Map<String, String>> extractDataByMouseInteraction(
            Page page, Locator canvas, BoundingBox bounds) {
        DataDeduplicator deduplicator = new DataDeduplicator();

        try {
            logger.info("开始鼠标交互提取数据...");
            logger.info("Canvas实际尺寸: width={}, height={}", bounds.width, bounds.height);

            // 重新计算图表的有效区域，专门针对折线图的数据点
            double chartMarginLeft = bounds.width * 0.12;   // 左边距12%（Y轴标签）
            double chartMarginRight = bounds.width * 0.08;  // 右边距8%
            double chartMarginTop = bounds.height * 0.12;   // 上边距12%（标题）
            double chartMarginBottom = bounds.height * 0.18; // 下边距18%（X轴标签）

            double startX = bounds.x + chartMarginLeft;
            double endX = bounds.x + bounds.width - chartMarginRight;
            double startY = bounds.y + chartMarginTop;
            double endY = bounds.y + bounds.height - chartMarginBottom;

            logger.info("计算的图表区域: startX={}, endX={}, startY={}, endY={}",
                       startX, endX, startY, endY);

            // 专门针对折线图数据点的采样策略
            int xSamplePoints = 8;  // X轴采样点数（覆盖所有时间点）
            int ySamplePoints = 6;  // Y轴采样点数（覆盖所有折线）

            double stepX = (endX - startX) / xSamplePoints;
            double stepY = (endY - startY) / ySamplePoints;

            logger.info("采样参数: X轴{}个点，Y轴{}个点，总计{}个采样点",
                       xSamplePoints, ySamplePoints, xSamplePoints * ySamplePoints);

            int successCount = 0;
            int totalAttempts = 0;

            // 优化采样策略：重点采样折线图的数据点位置
            for (int i = 0; i <= xSamplePoints; i++) {
                double currentX = startX + i * stepX;

                for (int j = 0; j <= ySamplePoints; j++) {
                    double currentY = startY + j * stepY;
                    totalAttempts++;

                    try {
                        // 移动鼠标到当前位置
                        page.mouse().move(currentX, currentY);
                        page.waitForTimeout(300); // 增加等待时间确保tooltip显示

                        // 尝试提取tooltip数据
                        String tooltipData = extractTooltipData(page);
                        if (!tooltipData.isEmpty() && isValidCoalData(tooltipData)) {
                            logger.debug("在位置({:.1f}, {:.1f})提取到有效数据: {}", currentX, currentY, tooltipData);
                            parseTooltipData(tooltipData, deduplicator);
                            successCount++;
                        }

                        // 每10个采样点输出一次进度
                        if (totalAttempts % 10 == 0) {
                            logger.info("采样进度: {}/{}, 成功提取: {}", totalAttempts,
                                       (xSamplePoints + 1) * (ySamplePoints + 1), successCount);
                        }

                    } catch (Exception e) {
                        logger.debug("在位置({:.1f}, {:.1f})提取数据失败: {}", currentX, currentY, e.getMessage());
                    }
                }
            }

            logger.info("鼠标交互完成，总采样点: {}, 成功提取: {}, 处理了{}条原始数据，去重后提取到{}天的数据",
                       totalAttempts, successCount, deduplicator.getProcessedCount(), deduplicator.getUniqueData().size());

        } catch (Exception e) {
            logger.error("鼠标交互提取数据失败: {}", e.getMessage(), e);
        }

        return deduplicator.getUniqueData();
    }

    /**
     * 验证是否为有效的煤炭数据
     */
    private static boolean isValidCoalData(String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含煤炭相关关键词
        String lowerData = data.toLowerCase();
        boolean hasCoalKeyword = lowerData.contains("kcal") || lowerData.contains("大卡") ||
                                lowerData.contains("外购") || lowerData.contains("神优");

        // 检查是否包含数字（价格信息）
        boolean hasNumbers = data.matches(".*\\d{3,}.*");

        // 排除无关数据
        boolean isNotAppData = !lowerData.contains("app下载") && !lowerData.contains("下载");

        return hasCoalKeyword && hasNumbers && isNotAppData;
    }

    /**
     * 提取tooltip数据 - 专门针对ECharts折线图的tooltip
     */
    private static String extractTooltipData(Page page) {
        try {
            // 首先尝试使用JavaScript直接获取ECharts的tooltip内容
            String jsTooltipData = extractTooltipUsingJavaScript(page);
            if (!jsTooltipData.isEmpty() && isValidCoalData(jsTooltipData)) {
                logger.debug("通过JavaScript获取到tooltip数据: {}", jsTooltipData);
                return jsTooltipData;
            }

            // ECharts专用的tooltip选择器
            String[] echartsTooltipSelectors = {
                ".echarts-tooltip",
                ".echarts-tooltip-content",
                "[class*='echarts-tooltip']",
                "div[style*='position: absolute'][style*='pointer-events: none']", // ECharts tooltip特征
                "div[style*='z-index'][style*='position: absolute']",
                ".tooltip-content",
                ".chart-tooltip-content"
            };

            for (String selector : echartsTooltipSelectors) {
                try {
                    Locator tooltipElement = page.locator(selector);
                    if (tooltipElement.count() > 0) {
                        for (int i = 0; i < tooltipElement.count(); i++) {
                            Locator element = tooltipElement.nth(i);
                            if (element.isVisible()) {
                                String text = element.textContent();
                                if (text != null && !text.trim().isEmpty() && isValidCoalData(text)) {
                                    logger.debug("通过ECharts选择器 {} 找到tooltip数据: {}", selector, text.trim());
                                    return text.trim();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("ECharts选择器 {} 失败: {}", selector, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.debug("提取tooltip数据失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 使用JavaScript直接获取ECharts的tooltip内容
     */
    private static String extractTooltipUsingJavaScript(Page page) {
        try {
            // 简化的JavaScript代码，避免复杂的字符串拼接
            String jsCode = "() => { " +
                "try { " +
                "var tooltips = document.querySelectorAll('.echarts-tooltip'); " +
                "for (var i = 0; i < tooltips.length; i++) { " +
                "if (tooltips[i].offsetParent !== null) { " +
                "var text = tooltips[i].textContent.trim(); " +
                "if (text && (text.indexOf('元') > -1 || text.indexOf('kCal') > -1)) { " +
                "return text; " +
                "} " +
                "} " +
                "} " +
                "var divs = document.querySelectorAll('div'); " +
                "for (var j = 0; j < divs.length; j++) { " +
                "var div = divs[j]; " +
                "if (div.style.position === 'absolute' && div.offsetParent !== null) { " +
                "var divText = div.textContent.trim(); " +
                "if (divText && divText.length > 5 && divText.length < 100 && " +
                "(divText.indexOf('元') > -1 || divText.indexOf('kCal') > -1 || divText.indexOf('外购') > -1)) { " +
                "return divText; " +
                "} " +
                "} " +
                "} " +
                "return ''; " +
                "} catch (e) { " +
                "return ''; " +
                "} " +
                "}";

            Object result = page.evaluate(jsCode);
            if (result instanceof String) {
                String tooltipText = (String) result;
                if (!tooltipText.isEmpty()) {
                    logger.debug("JavaScript提取到tooltip: {}", tooltipText);
                    return tooltipText;
                }
            }
        } catch (Exception e) {
            logger.debug("JavaScript提取tooltip失败: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 解析tooltip数据 - 专门针对ECharts折线图的tooltip格式
     */
    private static void parseTooltipData(String tooltipData, DataDeduplicator deduplicator) {
        try {
            logger.debug("开始解析tooltip数据: {}", tooltipData);

            // 清理数据，移除多余的空白字符
            String cleanedData = tooltipData.replaceAll("\\s+", " ").trim();

            // 多种日期格式匹配，优先匹配ECharts常用格式
            Pattern[] datePatterns = {
                Pattern.compile("(\\d{2}-\\d{2})"),           // 07-15 (最常见)
                Pattern.compile("(\\d{1,2}月\\d{1,2}日)"),      // 7月15日
                Pattern.compile("(\\d{4}-\\d{2}-\\d{2})"),    // 2025-07-15
                Pattern.compile("(\\d{2}/\\d{2})"),           // 07/15
                Pattern.compile("(\\d{1,2}-\\d{1,2})"),       // 7-15
            };

            String extractedDate = null;
            String remainingData = cleanedData;

            for (Pattern datePattern : datePatterns) {
                Matcher dateMatcher = datePattern.matcher(cleanedData);
                if (dateMatcher.find()) {
                    extractedDate = normalizeDateFormat(dateMatcher.group(1));
                    // 保留完整数据用于后续解析
                    remainingData = cleanedData;
                    logger.debug("提取到日期: {} (原格式: {})", extractedDate, dateMatcher.group(1));
                    break;
                }
            }

            // 如果没有找到日期，尝试使用当前日期
            if (extractedDate == null) {
                extractedDate = DATE_FORMAT.format(new Date());
                logger.debug("未找到日期，使用当前日期: {}", extractedDate);
                remainingData = cleanedData;
            }

            // 解析价格数据 - 针对ECharts tooltip的多种格式
            boolean foundData = false;

            // 格式1: 神华外购tooltip格式
            logger.debug("开始解析神华tooltip数据，原始数据: {}", remainingData);

            // 先处理神优类型
            Pattern shenhuaSpecialPattern = Pattern.compile("外购神优2(\\d{3,4})(?:\\（[^）]*\\）)?");
            Matcher shenhuaSpecialMatcher = shenhuaSpecialPattern.matcher(remainingData);
            while (shenhuaSpecialMatcher.find()) {
                String price = shenhuaSpecialMatcher.group(1);
                String calorific = "外购神优2";
                deduplicator.addData(extractedDate, calorific, price + "元", tooltipData);
                foundData = true;
                logger.debug("解析神华神优数据: {} {} = {}元", extractedDate, calorific, price);
            }

            // 再处理数字热值类型，需要精确分割热值和价格
            Pattern shenhuaNumberPattern = Pattern.compile("外购(4500|5000|5500)(\\d{3})(?:\\（[^）]*\\）)?");
            Matcher shenhuaNumberMatcher = shenhuaNumberPattern.matcher(remainingData);
            while (shenhuaNumberMatcher.find()) {
                String calorificValue = shenhuaNumberMatcher.group(1);
                String price = shenhuaNumberMatcher.group(2);
                String calorific = "外购" + calorificValue + "kCal";
                deduplicator.addData(extractedDate, calorific, price + "元", tooltipData);
                foundData = true;
                logger.debug("解析神华数字热值数据: {} {} = {}元", extractedDate, calorific, price);
            }

            // 格式2: CCI/CCTD tooltip格式
            logger.debug("开始解析CCI/CCTD tooltip数据，原始数据: {}", remainingData);

            Pattern cciTooltipPattern = Pattern.compile("(4500|5000|5500)kCal(\\d{3})(?:\\（[^）]*\\）)?");
            Matcher cciTooltipMatcher = cciTooltipPattern.matcher(remainingData);
            while (cciTooltipMatcher.find()) {
                String calorificValue = cciTooltipMatcher.group(1);
                String price = cciTooltipMatcher.group(2);

                String calorific = calorificValue + "kCal";

                deduplicator.addData(extractedDate, calorific, price + "元", tooltipData);
                foundData = true;
                logger.debug("解析CCI/CCTD tooltip数据: {} {} = {}元", extractedDate, calorific, price);
            }

            // 格式3: 通用格式 - 外购5500kCal=423元, 外购5000kCal=368元
            Pattern generalPattern = Pattern.compile("(外购)?(\\d{4,5}kCal?|神优2?)\\s*[=:]\\s*(\\d{3,})元?");
            Matcher generalMatcher = generalPattern.matcher(remainingData);
            while (generalMatcher.find()) {
                String prefix = generalMatcher.group(1) != null ? generalMatcher.group(1) : "";
                String calorific = prefix + generalMatcher.group(2);
                if (!calorific.contains("kCal") && !calorific.contains("神优")) {
                    calorific += "kCal";
                }
                String price = generalMatcher.group(3) + "元";
                deduplicator.addData(extractedDate, calorific, price, tooltipData);
                foundData = true;
                logger.debug("解析通用格式数据: {} = {}", calorific, price);
            }

            // 如果以上都没匹配到，尝试智能解析
            if (!foundData) {
                parseIntelligentFormat(remainingData, extractedDate, deduplicator, tooltipData);
            }

        } catch (Exception e) {
            logger.debug("解析tooltip数据失败: {}", e.getMessage());
        }
    }

    /**
     * 智能解析格式
     */
    private static void parseIntelligentFormat(String data, String date, DataDeduplicator deduplicator, String originalData) {
        try {
            // 查找所有数字
            Pattern numberPattern = Pattern.compile("\\d{3,}");
            Matcher numberMatcher = numberPattern.matcher(data);

            List<String> numbers = new ArrayList<>();
            while (numberMatcher.find()) {
                numbers.add(numberMatcher.group());
            }

            // 如果找到3-4个数字，可能是价格数据
            if (numbers.size() >= 3 && numbers.size() <= 4) {
                String[] calorificValues = {"5500kCal", "5000kCal", "4500kCal", "外购神优2"};

                for (int i = 0; i < Math.min(numbers.size(), calorificValues.length); i++) {
                    String price = numbers.get(i) + "元";
                    // 验证价格范围
                    try {
                        int priceValue = Integer.parseInt(numbers.get(i));
                        if (priceValue >= 200 && priceValue <= 1000) {
                            deduplicator.addData(date, calorificValues[i], price, originalData + "_intelligent_" + i);
                            logger.debug("智能解析数据: {} = {}", calorificValues[i], price);
                        }
                    } catch (NumberFormatException e) {
                        logger.debug("价格数字解析失败: {}", numbers.get(i));
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("智能解析失败: {}", e.getMessage());
        }
    }
}
