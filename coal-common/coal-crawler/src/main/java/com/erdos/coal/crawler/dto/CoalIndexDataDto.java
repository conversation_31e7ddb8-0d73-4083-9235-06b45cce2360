package com.erdos.coal.crawler.dto;

import com.erdos.coal.crawler.enums.IndexType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 煤炭指数数据DTO
 */
public class CoalIndexDataDto {
    
    /**
     * 指数类型
     */
    private IndexType indexType;
    
    /**
     * 数据日期
     */
    private Date dataDate;
    
    /**
     * 热值（大卡）
     */
    private Integer calorificValue;

    /**
     * 煤种类型标识：如"外购4500"、"外购5500"、"外购神优2"、"5500kCal"等
     */
    private String coalType;

    /**
     * 煤种分类：如"外购"、"进口"等
     */
    private String coalCategory;
    
    /**
     * 价格（元/吨）
     */
    private BigDecimal price;
    
    /**
     * 涨跌幅（%）
     */
    private BigDecimal changeRate;
    
    /**
     * 涨跌趋势
     */
    private String trendDirection;
    
    /**
     * 较上期变化金额（元）
     */
    private BigDecimal changeAmount;
    
    /**
     * 数据来源URL
     */
    private String sourceUrl;
    
    public CoalIndexDataDto() {}
    
    public CoalIndexDataDto(IndexType indexType, Date dataDate, Integer calorificValue, BigDecimal price) {
        this.indexType = indexType;
        this.dataDate = dataDate;
        this.calorificValue = calorificValue;
        this.price = price;
    }

    public CoalIndexDataDto(IndexType indexType, Date dataDate, String coalType, BigDecimal price) {
        this.indexType = indexType;
        this.dataDate = dataDate;
        this.coalType = coalType;
        this.price = price;

        // 尝试从coalType中提取热值
        if (coalType != null && coalType.matches(".*\\d{4}.*")) {
            try {
                String numStr = coalType.replaceAll("\\D", "");
                if (numStr.length() >= 4) {
                    this.calorificValue = Integer.parseInt(numStr.substring(0, 4));
                }
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }

        // 设置煤种分类
        if (coalType != null) {
            if (coalType.contains("外购")) {
                this.coalCategory = "外购";
            } else if (coalType.contains("kCal")) {
                this.coalCategory = "标准";
            } else {
                this.coalCategory = "其他";
            }
        }
    }
    
    public IndexType getIndexType() {
        return indexType;
    }
    
    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public Date getDataDate() {
        return dataDate;
    }
    
    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }
    
    public Integer getCalorificValue() {
        return calorificValue;
    }
    
    public void setCalorificValue(Integer calorificValue) {
        this.calorificValue = calorificValue;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getChangeRate() {
        return changeRate;
    }
    
    public void setChangeRate(BigDecimal changeRate) {
        this.changeRate = changeRate;
    }
    
    public String getTrendDirection() {
        return trendDirection;
    }
    
    public void setTrendDirection(String trendDirection) {
        this.trendDirection = trendDirection;
    }
    
    public BigDecimal getChangeAmount() {
        return changeAmount;
    }
    
    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }
    
    public String getSourceUrl() {
        return sourceUrl;
    }
    
    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public String getCoalType() {
        return coalType;
    }

    public void setCoalType(String coalType) {
        this.coalType = coalType;
    }

    public String getCoalCategory() {
        return coalCategory;
    }

    public void setCoalCategory(String coalCategory) {
        this.coalCategory = coalCategory;
    }
}
