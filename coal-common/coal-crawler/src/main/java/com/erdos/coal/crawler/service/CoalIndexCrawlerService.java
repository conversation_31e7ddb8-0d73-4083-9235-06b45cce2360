package com.erdos.coal.crawler.service;

import com.erdos.coal.crawler.dto.CoalIndexCrawlRequest;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.entity.CoalIndexData;
import com.erdos.coal.crawler.enums.IndexType;

import java.util.Date;
import java.util.List;

/**
 * 煤炭指数爬虫服务接口
 */
public interface CoalIndexCrawlerService {
    
    /**
     * 爬取煤炭指数数据
     */
    CoalIndexCrawlResponse crawlCoalIndexData(CoalIndexCrawlRequest request);
    
    /**
     * 爬取指定类型的最新数据
     */
    CoalIndexCrawlResponse crawlLatestData(IndexType indexType);
    
    /**
     * 爬取所有类型的最新数据
     */
    CoalIndexCrawlResponse crawlAllLatestData();
    
    /**
     * 查询煤炭指数数据
     */
    List<CoalIndexDataDto> queryCoalIndexData(IndexType indexType, Date startDate, Date endDate);
    
    /**
     * 查询最新的煤炭指数数据
     */
    List<CoalIndexDataDto> queryLatestData(IndexType indexType);
    
    /**
     * 获取爬虫执行历史
     */
    List<CoalIndexCrawlResponse> getCrawlHistory(IndexType indexType, int limit);
    
    /**
     * 手动触发数据爬取
     */
    CoalIndexCrawlResponse manualCrawl(IndexType indexType, Long timeout);
    
    /**
     * 检查数据是否需要更新
     */
    boolean needUpdate(IndexType indexType);

    /**
     * 专门提取图表数据
     */
    List<CoalIndexDataDto> extractChartData(IndexType indexType);

    /**
     * 提取煤易宝图表数据（原始格式）
     */
    Map<String, Map<String, String>> extractMeiyibaoChartData(IndexType indexType);
    
    /**
     * 获取数据统计信息
     */
    Object getDataStatistics(IndexType indexType);
}
