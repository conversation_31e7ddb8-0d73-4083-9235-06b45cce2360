package com.erdos.coal.crawler.service.impl;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.ChartDataExtractionService;
import com.erdos.coal.crawler.util.MeiyibaoChartExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 图表数据提取服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class ChartDataExtractionServiceImpl implements ChartDataExtractionService {

    private static final Logger logger = LoggerFactory.getLogger(ChartDataExtractionServiceImpl.class);

    @Override
    public List<CoalIndexDataDto> extractChartData(IndexType indexType) {
        try {
            logger.info("开始提取{}图表数据...", indexType.getName());
            
            List<CoalIndexDataDto> result = MeiyibaoChartExtractor.extractChartData(indexType);
            
            logger.info("{}图表数据提取完成，共获取{}条数据", indexType.getName(), result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("提取{}图表数据失败: {}", indexType.getName(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Map<String, String>> extractRawChartData(IndexType indexType) {
        try {
            logger.info("开始提取{}原始图表数据...", indexType.getName());
            
            Map<String, Map<String, String>> result = MeiyibaoChartExtractor.extractMeiyibaoData(indexType);
            
            logger.info("{}原始图表数据提取完成，共获取{}天的数据", indexType.getName(), result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("提取{}原始图表数据失败: {}", indexType.getName(), e.getMessage(), e);
            return new LinkedHashMap<>();
        }
    }

    @Override
    public Map<IndexType, List<CoalIndexDataDto>> extractAllChartData() {
        Map<IndexType, List<CoalIndexDataDto>> resultMap = new LinkedHashMap<>();
        
        logger.info("开始批量提取所有类型的图表数据...");
        
        for (IndexType indexType : getSupportedIndexTypes()) {
            try {
                List<CoalIndexDataDto> data = extractChartData(indexType);
                resultMap.put(indexType, data);
                logger.info("{}图表数据提取完成，共{}条", indexType.getName(), data.size());
            } catch (Exception e) {
                logger.error("提取{}图表数据失败: {}", indexType.getName(), e.getMessage(), e);
                resultMap.put(indexType, new ArrayList<>());
            }
        }
        
        int totalCount = resultMap.values().stream().mapToInt(List::size).sum();
        logger.info("批量图表数据提取完成，总计{}条数据", totalCount);
        
        return resultMap;
    }

    @Override
    public boolean isChartExtractionAvailable() {
        try {
            // 简单的可用性检查 - 尝试提取一个小样本
            logger.debug("检查图表数据提取功能可用性...");
            
            // 这里可以添加更复杂的检查逻辑，比如检查网络连接、浏览器可用性等
            // 目前简单返回true，实际使用中可以根据需要完善
            return true;
            
        } catch (Exception e) {
            logger.warn("图表数据提取功能不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public List<IndexType> getSupportedIndexTypes() {
        // 返回所有支持的指数类型
        return Arrays.asList(IndexType.SHENHUA, IndexType.CCI, IndexType.CCTD);
    }

    /**
     * 验证提取的数据质量
     */
    private boolean validateExtractedData(List<CoalIndexDataDto> data) {
        if (data == null || data.isEmpty()) {
            return false;
        }

        // 检查数据的基本完整性
        for (CoalIndexDataDto dto : data) {
            if (dto.getIndexType() == null || 
                dto.getDataDate() == null || 
                dto.getPrice() == null) {
                logger.warn("发现不完整的数据项: {}", dto);
                return false;
            }
        }

        return true;
    }

    /**
     * 数据后处理 - 清理和标准化数据
     */
    private List<CoalIndexDataDto> postProcessData(List<CoalIndexDataDto> rawData) {
        List<CoalIndexDataDto> processedData = new ArrayList<>();

        for (CoalIndexDataDto dto : rawData) {
            try {
                // 数据清理和标准化
                if (isValidDataItem(dto)) {
                    // 可以在这里添加数据标准化逻辑
                    processedData.add(dto);
                } else {
                    logger.debug("跳过无效数据项: {}", dto);
                }
            } catch (Exception e) {
                logger.warn("处理数据项失败: {}", e.getMessage());
            }
        }

        return processedData;
    }

    /**
     * 验证单个数据项是否有效
     */
    private boolean isValidDataItem(CoalIndexDataDto dto) {
        if (dto == null) return false;
        
        // 基本字段检查
        if (dto.getIndexType() == null || dto.getDataDate() == null || dto.getPrice() == null) {
            return false;
        }

        // 价格范围检查
        if (dto.getPrice().doubleValue() < 100 || dto.getPrice().doubleValue() > 2000) {
            return false;
        }

        // 热值检查
        if (dto.getCalorificValue() != null) {
            int calorific = dto.getCalorificValue();
            if (calorific < 3000 || calorific > 7000) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取数据提取统计信息
     */
    public Map<String, Object> getExtractionStatistics(IndexType indexType) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            List<CoalIndexDataDto> data = extractChartData(indexType);
            
            stats.put("indexType", indexType.getName());
            stats.put("totalCount", data.size());
            stats.put("extractionTime", new Date());
            
            // 按日期分组统计
            Map<String, Long> dateStats = new HashMap<>();
            data.forEach(dto -> {
                String dateKey = dto.getDataDate().toString();
                dateStats.put(dateKey, dateStats.getOrDefault(dateKey, 0L) + 1);
            });
            stats.put("dateDistribution", dateStats);
            
            // 按热值分组统计
            Map<String, Long> calorificStats = new HashMap<>();
            data.forEach(dto -> {
                String calorificKey = dto.getCoalType() != null ? dto.getCoalType() : "未知";
                calorificStats.put(calorificKey, calorificStats.getOrDefault(calorificKey, 0L) + 1);
            });
            stats.put("calorificDistribution", calorificStats);
            
        } catch (Exception e) {
            logger.error("获取提取统计信息失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
}
