package com.erdos.coal.crawler.service;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;

import java.util.List;
import java.util.Map;

/**
 * 图表数据提取服务接口
 * 专门负责从煤易宝网站提取图表数据
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface ChartDataExtractionService {

    /**
     * 提取指定类型的图表数据
     *
     * @param indexType 指数类型
     * @return 图表数据列表
     */
    List<CoalIndexDataDto> extractChartData(IndexType indexType);

    /**
     * 提取原始格式的图表数据
     *
     * @param indexType 指数类型
     * @return 原始格式数据 Map<日期, Map<热值, 价格>>
     */
    Map<String, Map<String, String>> extractRawChartData(IndexType indexType);

    /**
     * 批量提取所有类型的图表数据
     *
     * @return 所有类型的图表数据
     */
    Map<IndexType, List<CoalIndexDataDto>> extractAllChartData();

    /**
     * 检查图表数据提取功能是否可用
     *
     * @return 是否可用
     */
    boolean isChartExtractionAvailable();

    /**
     * 获取支持的指数类型列表
     *
     * @return 支持的指数类型
     */
    List<IndexType> getSupportedIndexTypes();
}
