# 图表数据提取功能整合文档

## 概述

本文档描述了将ChartDataExtractor类整合到coal-crawler模块的完整过程。整合后的功能提供了更强大、更灵活的煤易宝网站图表数据提取能力。

## 整合架构

### 1. 核心组件

#### 1.1 MeiyibaoChartExtractor (工具类)
- **位置**: `com.erdos.coal.crawler.util.MeiyibaoChartExtractor`
- **功能**: 专门用于提取煤易宝网站图表数据的核心工具类
- **特性**:
  - 支持神华外购、CCI指数、CCTD指数三种数据类型
  - 使用Playwright进行浏览器自动化
  - 支持ECharts实例数据直接提取
  - 支持鼠标交互方式提取tooltip数据
  - 内置数据去重和验证机制

#### 1.2 ChartDataExtractionService (服务接口)
- **位置**: `com.erdos.coal.crawler.service.ChartDataExtractionService`
- **功能**: 图表数据提取服务接口
- **方法**:
  - `extractChartData(IndexType)`: 提取指定类型的图表数据
  - `extractRawChartData(IndexType)`: 提取原始格式的图表数据
  - `extractAllChartData()`: 批量提取所有类型的图表数据
  - `isChartExtractionAvailable()`: 检查功能可用性
  - `getSupportedIndexTypes()`: 获取支持的指数类型

#### 1.3 ChartDataExtractionServiceImpl (服务实现)
- **位置**: `com.erdos.coal.crawler.service.impl.ChartDataExtractionServiceImpl`
- **功能**: 图表数据提取服务实现类
- **特性**:
  - 集成MeiyibaoChartExtractor功能
  - 提供数据验证和后处理
  - 支持统计信息收集

#### 1.4 ChartDataExtractionController (控制器)
- **位置**: `com.erdos.coal.crawler.controller.ChartDataExtractionController`
- **功能**: 提供图表数据提取的REST API接口
- **端点**:
  - `POST /api/coal/chart/extract/{indexType}`: 提取指定类型图表数据
  - `POST /api/coal/chart/extract-raw/{indexType}`: 提取原始格式图表数据
  - `POST /api/coal/chart/extract-all`: 批量提取所有类型图表数据
  - `GET /api/coal/chart/status`: 检查功能状态
  - `POST /api/coal/chart/test/{indexType}`: 测试功能

### 2. 集成到现有架构

#### 2.1 CoalIndexCrawlerService 扩展
在现有的`CoalIndexCrawlerService`接口中添加了新方法：
```java
List<CoalIndexDataDto> extractChartData(IndexType indexType);
Map<String, Map<String, String>> extractMeiyibaoChartData(IndexType indexType);
```

#### 2.2 PlaywrightCrawler 增强
在`PlaywrightCrawler`类中添加了新方法：
```java
List<CoalIndexDataDto> crawlChartDataOnly(IndexType indexType);
Map<String, Map<String, String>> crawlRawChartData(IndexType indexType);
List<CoalIndexDataDto> crawlCoalIndexDataEnhanced(IndexType indexType, Long timeout);
```

## 使用方式

### 1. 通过服务接口使用

```java
@Autowired
private ChartDataExtractionService chartDataExtractionService;

// 提取神华外购图表数据
List<CoalIndexDataDto> shenhuaData = chartDataExtractionService.extractChartData(IndexType.SHENHUA);

// 提取原始格式数据
Map<String, Map<String, String>> rawData = chartDataExtractionService.extractRawChartData(IndexType.CCI);

// 批量提取所有类型数据
Map<IndexType, List<CoalIndexDataDto>> allData = chartDataExtractionService.extractAllChartData();
```

### 2. 通过REST API使用

```bash
# 提取神华外购图表数据
curl -X POST http://localhost:8080/api/coal/chart/extract/SHENHUA

# 提取CCI指数原始数据
curl -X POST http://localhost:8080/api/coal/chart/extract-raw/CCI

# 批量提取所有数据
curl -X POST http://localhost:8080/api/coal/chart/extract-all

# 检查功能状态
curl -X GET http://localhost:8080/api/coal/chart/status

# 测试功能
curl -X POST http://localhost:8080/api/coal/chart/test/CCTD
```

### 3. 直接使用工具类

```java
// 提取原始格式数据
Map<String, Map<String, String>> rawData = MeiyibaoChartExtractor.extractMeiyibaoData(IndexType.SHENHUA);

// 提取DTO格式数据
List<CoalIndexDataDto> dtoData = MeiyibaoChartExtractor.extractChartData(IndexType.CCI);
```

## 数据格式

### 1. 原始格式 (Map<String, Map<String, String>>)
```
{
  "07-15": {
    "外购5500kCal": "423元",
    "外购5000kCal": "368元",
    "外购4500kCal": "298元",
    "外购神优2": "474元"
  },
  "07-16": {
    "外购5500kCal": "425元",
    "外购5000kCal": "370元",
    "外购4500kCal": "300元",
    "外购神优2": "476元"
  }
}
```

### 2. DTO格式 (List<CoalIndexDataDto>)
```java
CoalIndexDataDto {
    indexType: SHENHUA,
    dataDate: 2025-07-15,
    calorificValue: 5500,
    coalType: "外购5500kCal",
    price: 423,
    sourceUrl: "https://www.meiyibao.com/"
}
```

## 技术特性

### 1. 数据提取策略
- **ECharts实例直接提取**: 优先尝试从ECharts实例获取数据
- **鼠标交互提取**: 当直接提取失败时，使用鼠标交互触发tooltip
- **多重验证**: 对提取的数据进行格式和范围验证
- **智能去重**: 基于日期、热值、价格的组合进行去重

### 2. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 优雅的降级策略

### 3. 性能优化
- 浏览器资源管理
- 超时控制
- 采样策略优化

## 测试

### 1. 单元测试
运行`ChartDataExtractorTest`类进行功能测试：
```java
public static void main(String[] args) {
    // 测试新的MeiyibaoChartExtractor
    testNewMeiyibaoChartExtractor();
    
    // 测试兼容性
    testOriginalChartDataExtractor();
}
```

### 2. 集成测试
通过REST API进行集成测试：
```bash
# 测试神华外购数据提取
curl -X POST http://localhost:8080/api/coal/chart/test/SHENHUA
```

## 配置说明

### 1. 浏览器配置
- 默认使用Chromium浏览器
- 可视化模式便于调试（生产环境建议使用无头模式）
- 超时时间：120秒

### 2. 采样配置
- X轴采样点：8个
- Y轴采样点：6个
- 鼠标移动等待时间：300ms

## 兼容性

### 1. 向后兼容
- 保持原有API接口不变
- 原有功能继续可用
- 数据格式保持一致

### 2. 扩展性
- 支持新增指数类型
- 支持自定义数据提取策略
- 支持配置化参数调整

## 部署注意事项

### 1. 依赖要求
- Playwright库及其浏览器驱动
- 足够的系统资源支持浏览器运行
- 网络连接稳定

### 2. 环境配置
- 确保Playwright浏览器正确安装
- 配置适当的超时时间
- 监控系统资源使用情况

## 维护指南

### 1. 日志监控
- 关注提取成功率
- 监控响应时间
- 检查错误日志

### 2. 数据质量
- 定期验证提取数据的准确性
- 监控数据格式变化
- 及时调整提取策略

### 3. 性能调优
- 根据实际使用情况调整采样参数
- 优化超时配置
- 监控内存使用情况
